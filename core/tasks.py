# myapp/tasks.py

from celery import shared_task

@shared_task
def my_scheduled_task():
    print("This task runs every 5 seconds")

from celery import shared_task
from Project.models import Post, ThirdPartyAuth , decrypt_data
from Social.upload_post import upload_insta_video, upload_post_images_fun
from Vimeo.upload_vimeo import upload_vimeo_video
from helpers.id_decode import decode_token
from helpers.paginator import CustomPagination
from Project.link_upload_post import create_linkedin_post_with_multiple_media, create_linkedin_post_with_video, upload_media
from pinterest.upload_pin import post_pin, upload_video_to_pinterest
@shared_task
def handle_third_party_integrations(post_id, user_id, response_data, validated_data):
    try:
        post = Post.objects.get(id=post_id)
        third_party_data = ThirdPartyAuth.objects.get(user_id=user_id)
        original_files = ['media/' + file.split('/media/')[-1] for file in response_data['files']]
        
        if validated_data.get('instagram'):
            if third_party_data.instagram_check:
                if not validated_data.get('is_video'):
                    upload_post_images_fun(f'json/instagram/{decrypt_data(post.user.enc_email)}.json', original_files, validated_data.get('description'))
                else:
                    upload_insta_video(f'json/instagram/{decrypt_data(post.user.enc_email)}.json', original_files[0], validated_data.get('description'), validated_data.get('title'))
        
        if validated_data.get('linkedin'):
            if third_party_data.linkedin_check:
                unique_id = third_party_data.linkedin_creds
                token = third_party_data.linked_in_token
                media_types = ['image/' + file.split('.')[-1] for file in original_files] if not validated_data.get('is_video') else ['video/' + file.split('.')[-1] for file in original_files]
                if not validated_data.get('is_video'):
                    create_linkedin_post_with_multiple_media(token, unique_id, validated_data.get('description'), original_files, media_types)
                else:
                    create_linkedin_post_with_video(token, unique_id, validated_data.get('description'), original_files, media_types)
        
        if validated_data.get('pinterest') and third_party_data.pinterest_check:
            access_token = third_party_data.pinterest_creds
            if not validated_data.get('is_video'):
                post_pin(access_token, response_data['files'][0], validated_data.get('title'), validated_data.get('description'))
        
        if validated_data.get('vimeo') and validated_data.get('is_video') and third_party_data.vimeo_check:
            vimeo_token = third_party_data.vimeo_creds
            upload_vimeo_video(vimeo_token, original_files[0], validated_data.get('title'), validated_data.get('description'))

        if validated_data.get('devto') and third_party_data.devto_check:
            try:
                from devto.upload_blog import create_devto_article, convert_html_to_markdown, format_devto_tags

                # Convert description to markdown if it contains HTML
                markdown_content = convert_html_to_markdown(validated_data.get('description')) if validated_data.get('description') else validated_data.get('title')

                # Create the Dev.to article
                success, result = create_devto_article(
                    api_key=third_party_data.devto_api_key,
                    title=validated_data.get('title'),
                    body_markdown=markdown_content,
                    published=True,
                    description=validated_data.get('description')[:300] if validated_data.get('description') else None
                )

                if success:
                    post.devto_id = str(result.get('id', ''))
                    post.save()
                    print(f"Dev.to article created successfully: {result.get('id')}")
                else:
                    post.devto = False
                    post.save()
                    print(f"Dev.to upload failed: {result}")

            except Exception as e:
                post.devto = False
                post.save()
                print(f"Dev.to upload error: {e}")
    except Exception as e:
        print(f"Error in background task: {e}")